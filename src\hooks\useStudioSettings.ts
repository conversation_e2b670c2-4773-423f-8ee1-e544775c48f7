import { useZodForm } from "./useZodForm"
import { useEffect, useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { updataStudioSettings } from "@/lib/utils"
import { toast } from "sonner"
import { updataStudioSettingsSchema } from "@/schema/studio-settings.schema"

export const useStudioSettings = (
    id: string,
    screen?: string | null,
    audio?: string | null,
    preset?: 'HD' | 'SD',
    plan?: 'PRO' | 'FREE',
    userId?: string | null
) => {
    const [onPreset, setOnPreset] = useState<'HD' | 'SD' | undefined>()

    const {register, watch } = useZodForm(updataStudioSettingsSchema, {
        screen: screen || '',
        audio: audio || '',
        preset: preset || 'HD',
    })

    const {mutate, isPending} = useMutation({
        mutationKey: ['update-studio'],
        mutationFn: (data: {
            screen: string
            audio: string
            preset: 'HD' | 'SD'
            id: string
        }) => updataStudioSettings(data.screen, data.audio, data.preset, data.id),
        onSuccess: (data)=> {
            return toast(data.status === 200 ? 'success' : 'error' ,{
                description: data.message || 'Settings updated',
            } )
        },
        onError: (error: any) => {
            console.error('Failed to update studio settings:', error)
            return toast('error', {
                description: 'Failed to update settings. Please try again.',
            })
        }
    })

    useEffect(() => {
        if(screen && audio && preset){
            window.ipcRenderer.send('media-sources', {
                screen,
                audio,
                preset,
                plan,
                id
            })
        }
    }, [screen, audio, preset, plan, id])

    useEffect(() => {
        const subscribe = watch((values) => {
            setOnPreset(values.preset)
            mutate({
                screen: values.screen!,
                audio: values.audio!,
                preset: values.preset!,
                id,
            })

            window.ipcRenderer.send('media-sources', {
                screen: values.screen!,
                audio: values.audio!,
                preset: values.preset!,
                plan,
                id
            })
        })

        return () => {
            subscribe.unsubscribe()
        }
    }, [watch])

    return {
        register,
        isPending,
        onPreset
    }
        
}